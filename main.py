from maix import image, display, app, time, camera, touchscreen
import cv2
import numpy as np
import math
import gc
from micu_uart_lib import (
    SimpleUART, micu_printf, bind_variable,
    VariableContainer, clear_variable_bindings
)

# --------------------------- 紫色激光检测器类 (注释版本) ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        # 紫激光检测代码先注释，看后续需要而定
        # hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        # lower_purple = np.array([130, 80, 80])
        # upper_purple = np.array([160, 255, 255])
        # mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        # mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        # 
        # contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        # laser_points = []
        # 
        # for cnt in contours_purple:
        #     rect = cv2.minAreaRect(cnt)
        #     cx, cy = map(int, rect[0])
        #     laser_points.append((cx, cy))
        #     cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
        #     cv2.putText(img, "Laser", (cx-20, cy-10),
        #                cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        return img, []  # 暂时返回空的激光点列表

# --------------------------- 虚拟按键类 ---------------------------
class VirtualButtons:
    def __init__(self):
        # 按键定义 [x, y, width, height, text, action] - 放在屏幕最下方
        self.buttons = [
            # Center和Circle两个按键 - 都放在屏幕最下方 (y=180)
            [20, 180, 45, 20, "Center", "center"],    # 中心点模式 (默认) - 屏幕下方
            [115, 180, 50, 20, "Circle", "circle"],   # 圆形模式 - 屏幕下方
            # 二值化阈值调整按键 - 放在旁边
            [180, 180, 25, 20, "T-", "thresh_down"],  # 阈值减少按键
            [210, 180, 25, 20, "T+", "thresh_up"]     # 阈值增加按键
        ]

        # 触摸检测区域 [x, y, width, height] - 保持原始准确区域
        self.touch_areas = [
        [40, 370, 90, 40],         
        [230, 360, 100, 40],
        [360, 360, 50, 40],
        [420, 360, 50, 40]
        ]

        self.last_touch_time = 0
        self.touch_debounce = 0.3  # 300ms防抖动

    def check_touch(self, touch_x, touch_y):
        """检查触摸点击"""
        current_time = time.time()
        if current_time - self.last_touch_time < self.touch_debounce:
            return None

        print(f"Touch detected at: ({touch_x}, {touch_y})")

        for i, touch_area in enumerate(self.touch_areas):
            area_x, area_y, area_w, area_h = touch_area[0], touch_area[1], touch_area[2], touch_area[3]
            btn_text = self.buttons[i][4]

            # 检查触摸点是否在触摸区域内
            if area_x <= touch_x <= area_x + area_w and area_y <= touch_y <= area_y + area_h:
                print(f"Button '{btn_text}' HIT: touch_area({area_x},{area_y},{area_w},{area_h})")
                self.last_touch_time = current_time
                return self.buttons[i][5]  # 返回action
            else:
                print(f"Button '{btn_text}' miss: touch_area({area_x},{area_y},{area_w},{area_h})")

        print("No button hit")
        return None

    def draw_buttons(self, img, current_mode, threshold=46):
        """绘制虚拟按键"""
        for button in self.buttons:
            x, y, w, h, text, action = button

            # 根据当前模式高亮按钮
            if (action == "center" and current_mode == "center") or \
               (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)  # 黄色高亮
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)    # 绿色阈值按键
                thickness = 2
            else:
                color = (255, 255, 255)  # 白色普通
                thickness = 2

            # 绘制按钮边框
            cv2.rectangle(img, (x, y), (x + w, y + h), color, thickness)

            # 绘制按钮文字
            text_x = x + (w - len(text) * 4) // 2
            text_y = y + (h + 6) // 2
            cv2.putText(img, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # 显示当前阈值
        cv2.putText(img, f"Thresh: {threshold}", (180, 170),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

# 触摸屏初始化
def init_touchscreen():
    """初始化触摸屏"""
    try:
        ts = touchscreen.TouchScreen()
        print("TouchScreen initialized successfully")
        return ts
    except Exception as e:
        print(f"TouchScreen init failed: {e}")
        return None

# --------------------------- 圆形轨迹点生成函数 (来自730版本) ---------------------------
def generate_circle_points(center, radius, num_points):
    """在校正后的矩形内生成圆形轨迹点"""
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

# --------------------------- 透视变换工具函数 (来自730版本) ---------------------------
def perspective_transform(pts, target_width, target_height):
    """对四边形进行透视变换"""
    # 顶点排序（左上→右上→右下→左下）
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)  # 逆矩阵用于映射回原图
    return M, M_inv, src_pts

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    gc.disable()
    print("融合版jiguangcar程序启动...")
    
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)  # 使用初版的高分辨率
    laser_detector = PurpleLaserDetector()
    
    # 初始化虚拟按键和触摸屏
    buttons = VirtualButtons()
    ts = init_touchscreen()
    current_mode = "center"  # 默认选择center模式
    last_touch_pos = (0, 0)  # 触摸调试信息
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("$$", "##", True)  # 设置帧头尾为 $$...##
    else:
        print("串口初始化失败")
        exit()

    # 核心参数 (采用初版的更合理参数)
    min_contour_area = 500      # 初版参数：更大的最小面积
    max_contour_area = 70000    # 初版参数：更大的最大面积
    target_sides = 4
    binary_threshold = 66       # 二值化阈值，可通过按键调整
    
    # 透视变换与圆形参数 (来自730版本)
    corrected_width = 200    # 校正后矩形宽度
    corrected_height = 150   # 校正后矩形高度
    circle_radius = 50       # 校正后矩形内圆的半径
    circle_num_points = 12   # 圆周点数量
    
    # FPS计算初始化
    fps = 0
    last_time = time.ticks_ms()
    frame_count = 0

    while not app.need_exit():
        frame_count += 1
        
        # 处理触摸输入
        current_time = time.time()
        if ts and (current_time - buttons.last_touch_time) > buttons.touch_debounce:
            try:
                if ts.available():
                    touch_data = ts.read()
                    if len(touch_data) >= 3:
                        touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                        last_touch_pos = (touch_x, touch_y)  # 更新触摸位置用于显示

                        # 只处理按下事件
                        if pressed:
                            action = buttons.check_touch(touch_x, touch_y)
                            if action:
                                buttons.last_touch_time = current_time
                                if action == "center":
                                    current_mode = "center"
                                    print("切换到中心点模式")
                                elif action == "circle":
                                    current_mode = "circle"
                                    print("切换到圆形模式")
                                elif action == "thresh_up":
                                    binary_threshold = min(255, binary_threshold + 3)
                                    print(f"阈值增加到: {binary_threshold}")
                                elif action == "thresh_down":
                                    binary_threshold = max(1, binary_threshold - 3)
                                    print(f"阈值减少到: {binary_threshold}")
                                print(f"Touch: {action} at ({touch_x}, {touch_y})")
            except Exception as e:
                if frame_count % 120 == 0:  # 每120帧打印一次错误
                    print(f"Touch processing error: {e}")
        
        # 计算FPS
        current_time_ms = time.ticks_ms()
        if current_time_ms - last_time > 0:
            fps = 1000.0 / (current_time_ms - last_time)
        last_time = current_time_ms
        
        # 读取图像
        img = cam.read()
        if img is None:
            continue
        img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
        output = img_cv.copy()

        # 1. 矩形检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 只检测面积最大的一个矩形
        inner_quads = []
        if quads:
            # 找到面积最大的矩形
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]  # 只保留面积最大的一个
            pass
        else:
            pass

        # 2. 根据模式处理数据
        center_points = []  # 存储中心点
        all_circle_points = []  # 存储圆轨迹点

        for approx, area in inner_quads:
            # 提取顶点
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 绘制内框轮廓
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

            # 使用透视变换校正获得准确中心点
            M, M_inv, src_pts = perspective_transform(pts, corrected_width, corrected_height)
            if M_inv is not None:
                # 校正后矩形的真正几何中心
                corrected_center = (corrected_width//2, corrected_height//2)
                # 将校正后的中心点映射回原图
                center_np = np.array([[corrected_center]], dtype=np.float32)
                original_center = cv2.perspectiveTransform(center_np, M_inv)[0][0]
                cx, cy = int(original_center[0]), int(original_center[1])

                # 绘制校正后的中心点
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))
            else:
                # 备用方案：使用简单几何中心
                cx = int(np.mean(pts[:, 0]))
                cy = int(np.mean(pts[:, 1]))
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))

            # 圆形模式处理（无论透视变换是否成功都要执行）
            if current_mode == "circle":
                # 圆形模式：生成圆轨迹点
                if M_inv is not None:
                    # 使用透视变换校正的圆轨迹
                    corrected_center = (corrected_width//2, corrected_height//2)
                    corrected_circle = generate_circle_points(
                        corrected_center, circle_radius, circle_num_points
                    )

                    # 将校正后的圆轨迹点映射回原图
                    corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                    original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                    original_points = [(int(x), int(y)) for x, y in original_points]
                    all_circle_points.extend(original_points)

                    # 绘制映射回原图的轨迹点（红色）
                    for (x, y) in original_points:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
                else:
                    # 备用方案：在原图上直接生成圆轨迹
                    simple_circle = generate_circle_points((cx, cy), 30, circle_num_points)
                    all_circle_points.extend(simple_circle)

                    # 绘制简单圆轨迹点（红色）
                    for (x, y) in simple_circle:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)

        # 3. 激光检测 (暂时注释)
        output, laser_points = laser_detector.detect(output)

        # 4. 串口发送数据
        if current_mode == "center":
            # 中心点模式：发送单个中心点 (采用初版逻辑)
            if center_points:
                cx, cy = center_points[0]  # 只取第一个中心点
                micu_printf(f"R,{cx},{cy}")
            else:
                micu_printf("R,0,0")  # 无检测时发送(0,0)
        elif current_mode == "circle":
            # 圆形模式：发送圆的一圈坐标 (采用730版本逻辑)
            if all_circle_points:
                circle_data = f"C,{len(all_circle_points)}"
                for (x, y) in all_circle_points:
                    circle_data += f",{x},{y}"
                micu_printf(circle_data)

        # 5. 绘制目标点标记 - 紫色小十字标出(150,95)
        target_x, target_y = 150, 95
        cross_size = 5  # 十字大小
        # 绘制紫色十字
        cv2.line(output, (target_x - cross_size, target_y), (target_x + cross_size, target_y), (255, 0, 255), 2)  # 水平线
        cv2.line(output, (target_x, target_y - cross_size), (target_x, target_y + cross_size), (255, 0, 255), 2)  # 垂直线
        # 添加坐标标签
        cv2.putText(output, f"({target_x},{target_y})", (target_x + 8, target_y - 8),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

        # 6. 绘制虚拟按键
        buttons.draw_buttons(output, current_mode, binary_threshold)

        # 7. 显示统计信息
        cv2.putText(output, f"FPS: {fps:.1f}", (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 显示当前模式
        mode_text = f"Mode: {current_mode.upper()}"
        cv2.putText(output, mode_text, (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        # 显示触摸调试信息
        cv2.putText(output, f"Touch: {last_touch_pos}", (10, 55),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 0), 1)

        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)
